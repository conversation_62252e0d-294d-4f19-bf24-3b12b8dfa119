'use client'

import React, {
    useEffect,
    useRef,
    useState,
    DragE<PERSON>,
    ChangeEvent,
} from 'react'
import Image from 'next/image'
import AWS from 'aws-sdk'
import { Loader2 } from 'lucide-react'

import { AlertDialogNew } from '@/components/ui'
import { Input } from '@/components/ui/input'
import { cn } from '@/app/lib/utils'
import { toast } from '@/hooks/use-toast'

const ACCOUNT_ID = 'ddde1c1cd1aa25641691808dcbafdeb7'
const ACCESS_KEY_ID = '06c3e13a539f24e6fdf7075bf381bf5e'
const SECRET_ACCESS_KEY =
    '0bc23db8559504fb300b54def562d007e4a373fb940a7d07617ce906c553bbe8'

const s3Client = new AWS.S3({
    endpoint: `https://${ACCOUNT_ID}.r2.cloudflarestorage.com`,
    accessKeyId: ACCESS_KEY_ID,
    secretAccessKey: SECRET_ACCESS_KEY,
    signatureVersion: 'v4',
    region: 'auto',
})

// Type definitions
interface CloudFlareFile {
    title: string
}

interface UploadCloudFlareProps {
    files: CloudFlareFile[]
    setFiles: (
        files:
            | CloudFlareFile[]
            | ((prev: CloudFlareFile[]) => CloudFlareFile[]),
    ) => void
    multipleUpload?: boolean
    text?: string
    subText?: string
    bgClass?: string
}

export default function UploadCloudFlare({
    files,
    setFiles,
    multipleUpload = true,
    text = 'Documents and Images',
    subText,
    bgClass = '',
}: UploadCloudFlareProps) {
    /* ------------------------------------------------------- */
    /* state / refs                                            */
    /* ------------------------------------------------------- */
    const [dragActive, setDragActive] = useState<boolean>(false)
    const inputRef = useRef<HTMLInputElement>(null)
    const [imageLoader, setImageLoader] = useState<boolean>(false)
    const [image, setImage] = useState<string>('')
    const [displayImage, setDisplayImage] = useState<boolean>(false)
    const [clientID, setClientID] = useState<number>(0)

    useEffect(() => {
        setClientID(+(localStorage.getItem('clientId') ?? 0))
    }, [])

    /* ------------------------------------------------------- */
    /* helpers                                                 */
    /* ------------------------------------------------------- */
    const dropZoneClasses = cn(
        'relative flex w-full flex-col items-center justify-center rounded-lg border-2 border-dashed p-6 transition-colors focus-visible:outline-none',
        dragActive ? 'bg-accent border-primary' : 'bg-accent/50 border-border',
        'text-foreground hover:bg-accent hover:border-primary',
        'min-h-[10rem] cursor-pointer select-none',
        bgClass,
    )

    const uploadFile = async (file: File) => {
        const fileName = clientID + '-' + file.name
        const isFileExists = files.some(
            (existingFile: CloudFlareFile) => existingFile.title === fileName,
        )

        if (isFileExists) {
            toast({
                description: 'File with same name already exists!',
                variant: 'destructive',
            })
            return
        }

        setImageLoader(true)

        // Upload file to Cloudflare
        s3Client.putObject(
            {
                Bucket: 'sealogs',
                Key: fileName,
                Body: file,
            },
            (err, _data) => {
                setImageLoader(false)
                if (err) {
                    console.error(err)
                    toast({
                        description: 'Failed to upload file',
                        variant: 'destructive',
                    })
                } else {
                    const newFile: CloudFlareFile = { title: fileName }
                    if (multipleUpload) {
                        setFiles((prevState: CloudFlareFile[]) => [
                            ...prevState,
                            newFile,
                        ])
                    } else {
                        setFiles([newFile])
                    }
                }
            },
        )
    }

    /* ------------------------------------------------------- */
    /* event handlers                                          */
    /* ------------------------------------------------------- */
    const handleFiles = (fileList: FileList) => {
        const arr = Array.from(fileList)
        arr.forEach(uploadFile)
    }

    const onChange = (e: ChangeEvent<HTMLInputElement>) => {
        if (e.target.files) handleFiles(e.target.files)
    }

    const onDrop = (e: DragEvent<HTMLFormElement>) => {
        e.preventDefault()
        setDragActive(false)
        if (e.dataTransfer.files) handleFiles(e.dataTransfer.files)
    }

    const onDragToggle = (state: boolean) => (e: DragEvent) => {
        e.preventDefault()
        setDragActive(state)
    }

    const openFileExplorer = () => {
        if (inputRef.current) {
            inputRef.current.value = ''
            inputRef.current.click()
        }
    }

    const getFile = (fileName: string) => () => {
        // Download file from Cloudflare
        s3Client.getObject(
            {
                Bucket: 'sealogs',
                Key: fileName,
            },
            async (err, data) => {
                if (err) {
                    console.error(err)
                    toast({
                        description: 'Failed to download file',
                        variant: 'destructive',
                    })
                } else {
                    const fileType = fileName.split('.').pop() || ''
                    const blob = new Blob([data?.Body as Uint8Array])
                    const url = URL.createObjectURL(blob)

                    if (fileType.match(/^(jpg|jpeg|png|gif|bmp)$/i)) {
                        setImage(url)
                        setDisplayImage(true)
                    } else if (fileType.match(/^(pdf)$/i)) {
                        const pdfBlob = new Blob([data?.Body as Uint8Array], {
                            type: 'application/pdf',
                        })
                        const pdfUrl = URL.createObjectURL(pdfBlob)
                        window.open(pdfUrl, '_blank')
                        URL.revokeObjectURL(pdfUrl)
                    } else {
                        toast({
                            description:
                                'File type not supported to view. Please save the file to view.',
                            variant: 'destructive',
                        })
                        const link = document.createElement('a')
                        link.target = '_blank'
                        link.href = url
                        link.download = fileName
                        link.click()
                        URL.revokeObjectURL(url)
                    }
                }
            },
        )
    }

    /* ------------------------------------------------------- */
    /* render                                                  */
    /* ------------------------------------------------------- */
    return (
        <div className="w-full pt-4 lg:pt-0">
            {/* uploaded files display */}
            {files.length > 0 && (
                <div className="flex flex-wrap gap-4 mb-4">
                    {files
                        .filter(
                            (file: CloudFlareFile) => file.title?.length > 0,
                        )
                        .map((file: CloudFlareFile, index: number) => (
                            <div
                                key={index}
                                onClick={getFile(file.title)}
                                className="flex flex-col cursor-pointer items-center justify-center w-20 p-2 rounded-lg border border-border hover:border-primary transition-colors overflow-hidden">
                                <Image
                                    src="/sealogs-document_upload.svg"
                                    alt="Document"
                                    width={48}
                                    height={48}
                                    className="mb-2"
                                />
                                <div className="text-xs text-center break-all text-muted-foreground">
                                    {file.title.replace(clientID + '-', '')}
                                </div>
                            </div>
                        ))}
                </div>
            )}

            {/* upload form */}
            <form
                className={dropZoneClasses}
                onSubmit={(e) => e.preventDefault()}
                onDragEnter={onDragToggle(true)}
                onDragOver={onDragToggle(true)}
                onDragLeave={onDragToggle(false)}
                onDrop={onDrop}
                onClick={openFileExplorer}
                aria-label="File uploader drop zone">
                {/* heading */}
                <span className="absolute top-4 left-4 text-xs font-medium uppercase tracking-wider">
                    {text}
                </span>

                {/* hidden native input */}
                <Input
                    ref={inputRef}
                    type="file"
                    className="hidden"
                    multiple={multipleUpload}
                    accept=".xlsx,.xls,image/*,.doc,.docx,.ppt,.pptx,.txt,.pdf,.csv"
                    onChange={onChange}
                />

                {/* interactive area */}
                <div className="flex flex-col items-center gap-2 pointer-events-none">
                    <Image
                        src="/sealogs-document_upload.svg"
                        alt="Upload illustration"
                        width={96}
                        height={96}
                        className="relative -translate-x-2.5"
                        priority
                    />
                    {subText && (
                        <span className="text-sm font-medium text-neutral-400">
                            {subText}
                        </span>
                    )}
                </div>
            </form>

            {/* loader & image preview dialog */}
            {imageLoader && (
                <div className="mt-4 flex items-center justify-center gap-2">
                    <Loader2 className="h-5 w-5 animate-spin text-primary" />
                    <span className="text-sm text-muted-foreground">
                        Uploading...
                    </span>
                </div>
            )}

            <AlertDialogNew
                openDialog={displayImage}
                setOpenDialog={setDisplayImage}
                noButton
                actionText="Close"
                title="Image Preview">
                <div className="flex items-center justify-center">
                    <img
                        src={image}
                        alt="Preview"
                        className="max-w-full max-h-96 object-contain"
                    />
                </div>
            </AlertDialogNew>
        </div>
    )
}
